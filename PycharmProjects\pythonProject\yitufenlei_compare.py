
import json
import pandas as pd
import os

def jsonl_to_excel(jsonl_file_path, excel_file_path):
    """
    Reads a .jsonl file, extracts 'text', 'truth', and 'pred' fields,
    and writes them to an Excel file.

    Args:
        jsonl_file_path (str): The path to the input .jsonl file.
        excel_file_path (str): The path to the output .xlsx file.
    """
    data = []
    with open(jsonl_file_path, 'r', encoding='utf-8') as f:
        for line in f:
            try:
                # Parse the JSON object from each line
                json_obj = json.loads(line.strip())
                
                # Extract the required fields
                text = json_obj.get('text', '')
                truth = json_obj.get('truth', '')
                pred = json_obj.get('pred', '')
                
                # Append to the data list
                data.append([text, truth, pred])
            except json.JSONDecodeError:
                print(f"Skipping invalid JSON line: {line.strip()}")

    # Create a pandas DataFrame
    df = pd.DataFrame(data, columns=['text', 'truth', 'pred'])

    # Write the DataFrame to an Excel file
    df.to_excel(excel_file_path, index=False)
    print(f"Successfully converted {jsonl_file_path} to {excel_file_path}")

if __name__ == '__main__':
    # --- Configuration ---
    # Get the directory of the script
    script_dir = os.path.dirname(os.path.abspath(__file__))
    
    # Define the input and output file names
    input_filename = 'input.jsonl'
    output_filename = 'output.xlsx'
    
    # Create the full paths
    jsonl_input_path = os.path.join(script_dir, input_filename)
    excel_output_path = os.path.join(script_dir, output_filename)
    # --- End Configuration ---

    # Create a dummy input file for demonstration if it doesn't exist
    if not os.path.exists(jsonl_input_path):
        print(f"Input file not found. Creating a sample '{input_filename}' for demonstration.")
        with open(jsonl_input_path, 'w', encoding='utf-8') as f:
            f.write('{"text": "暂时放的23哇", "truth": "报价", "pred": "报量"}\n')
            f.write('{"text": "这债的区间是多少呀", "truth": "问量", "pred": "问价"}\n')
            f.write('{"text": "明天有新债吗", "truth": "其他", "pred": "问量"}\n')

    # Run the conversion
    jsonl_to_excel(jsonl_input_path, excel_output_path)
